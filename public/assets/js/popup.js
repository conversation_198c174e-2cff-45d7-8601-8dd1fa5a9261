if (!(typeof closeModal == 'function')) {
    function closeModal() {
        try {
            $('#exampleModal1').modal('toggle');
        } catch (e) {
        }
    }
}
if (!(typeof BlockDiv == 'function')) {
    function BlockDiv(blockID) {
        KTApp.block('#' + blockID, {
            overlayColor: '#000000',
            state: 'danger',
            message: 'Please wait...'
        });
    }
}
if (!(typeof UnBlockDiv == 'function')) {
    function UnBlockDiv(blockID) {
        KTApp.unblock('#' + blockID);
    }
}

var LWJsFunc = function () {
    // Private functions
    if (typeof tinymceInit !== 'undefined') {
        tinymce.remove('#taskComments');  //remove any existing instances of tinymce when trying to load it again, task tab in this case was the issue.
    }
    var tinymceInit = function () {
        console.log('Called TinyMCe');
        //NEXT BLOCK TAKEN FROM _customPage.js as it is a great version (removed old version here)
        tinymce.init({
            selector: '.tinyMceClass',
            forced_root_block: 'div',
            content_style: "body {font-size: 12pt;}",
            plugins: 'advlist autolink link image paste lists charmap print preview code table hr',
            menubar: false,
            toolbar: ['styleselect | fontselect | fontsizeselect | undo redo | cut copy paste | bold italic | link image | alignleft aligncenter alignright alignjustify | bullist numlist | outdent indent | blockquote subscript superscript | advlist | autolink | lists charmap | print preview | code | table tabledelete | tableprops tablerowprops tablecellprops | tableinsertrowbefore tableinsertrowafter tabledeleterow | tableinsertcolbefore tableinsertcolafter tabledeletecol | hr'],
            browser_spellcheck: true,
            contextmenu: false,
            //contextmenu false - this makes it so you can right click on misspelled and get correction suggestion, without this tinymce wants to make it a hyperlink
            image_title: true,
            file_picker_types: 'image',
            width: '100%',
            height: 200,
            autoresize_min_height: 200,
            autoresize_max_height: 400,
            relative_urls: false,
            remove_script_host: false,
            branding: false,
            block_unsupported_drop: true,
            images_file_types: 'jpeg,jpg,png,gif,JPEG,JPG,PNG,GIF',
            images_upload_url: '/backoffice/api_v2/tinymce_upload?mailtype=fileemail',
            paste_data_images: true,
            //LoanInfoTab
            //Additional Terms & Requirements for Loan Processing & Underwriting
            setup: function (editor) {
                editor.on('input', function () {
                    let submit = $('input[type=submit]');
                    if (submit.length > 0) {
                        submit.prop('disabled', false);
                    }
                });
            }
        });

    }

    var dateTimePicker = function () {

        $('.dateTimePickerClass').datetimepicker({
            autoclose: true,
            //calendarWeeks : true,
            clearBtn: true,
            showButtonPanel: true,
            minDate: new Date(),
            disabledDates: [new Date()],
            dateFormat: 'mm/dd/yy',
            timeFormat: 'hh:mm:am',
            todayHighlight: true,
            onChangeMonthYear: function (year, month, inst) {
                //  setTimeout("setServerTimeToSchedule()", 100);
            },
        });
        $(".dateTimePickerClass").click(function () {
            $(this).datetimepicker('show');
            // setServerTimeToSchedule();
        });

        /*     $('.datepickerClass').on('click', function () {
        $(this).data('datetimepicker').show();
        });*/

        /*   $('#kt_datetimepicker_12').on('blur', function () {
        $(this).data('datetimepicker').hide();
        });*/
    }

    var datePickerFu = function () { // Future Dates
        $('.datePickerFu').datepicker({
            autoclose: true,
            clearBtn: true,
            showButtonPanel: true,
            startDate: '0d',
            todayHighlight: true,
            startDate: '01/01/1900',
        }).focus();
    }

    var datePickerFuOl = function () { //Old Future Dates
        $('.datePickerFuOl').datepicker({
            autoclose: true,
            clearBtn: true,
            showButtonPanel: true,
            todayHighlight: true,
            startDate: '01/01/1900',
        }).focus();
    }

    var manualPopover = function () {
        $(".manualPopover").popover({
            trigger: "manual", html: true, sanitize: false, animation: true,
            template: '\
            <div class="popover popOverManual p-0" role="tooltip">\
                <div class="arrow"></div>\
                <h3 class="popover-header"></h3>\
                <div class="popover-body p-2"></div>\
            </div>'
        }).on("mouseenter", function () {
            var _this = this;
            $(this).popover("show");
            $(".popover").on("mouseleave", function () {
                $(_this).popover('hide');
            });
        }).on("mouseleave", function () {
            var _this = this;
            setTimeout(function () {
                if (!$(".popover:hover").length) {
                    $(_this).popover("hide");
                }
            }, 300);
        });
    }

    var popOverClass = function () {
        $(".popoverClass").popover({
            trigger: "manual", html: true, sanitize: false, animation: true,
            template: '<div class="popover" role="tooltip"><div class="arrow"></div><h3 class="popover-header text-center"></h3><div class="popover-body"></div></div>'
        }).on("mouseenter", function () {
            var _this = this;
            $(this).popover("show");
            $(".popover").on("mouseleave", function () {
                $(_this).popover('hide');
            });
        }).on("mouseleave", function () {
            var _this = this;
            setTimeout(function () {
                if (!$(".popover:hover").length) {
                    $(_this).popover("hide");
                }
            }, 300);
        });
    }

    return {
        init: function () {
            tinymceInit();
            dateTimePicker();
            datePickerFu();
            datePickerFuOl();
            manualPopover();
            popOverClass();
        }
    };
}();

$(document).ready(function () {

    $(document).on('click', 'a.tooltipClass', function () {
        $('.tooltip').hide();
    });
    $(document).on('mouseenter', 'a.tooltipClass', function () {
        $(this).tooltip({
            boundary: 'window',
        }).tooltip('show');
    });

    $('.tooltipClass').tooltip({
        boundary: 'window',
    });

    $('[data-toggle="tooltip"]').tooltip({
        boundary: 'window',
    });
    $('[data-toggle="popover"]').popover();

    $('.chzn-selectBootStrap').select2({
        theme: "bootstrap"
    });

    $(".chzn-select").chosen({
        allow_single_deselect: true,
        search_contains: true
    }); // With Red Close --Add Option Available
    $(".chzn-select1").chosen({
        allow_single_deselect: true,
        search_contains: true
    });   // With Red Close --Add Option Available

    $('.chzn-selectShowSelectAll').selectpicker();
    /* https://developer.snapappointments.com/bootstrap-select/examples/ */
    $('[data-switch=true]').bootstrapSwitch();

    LWJsFunc.init();
});

function showAndHideNotifyUsers() {
    let notifyUsers = parseInt($("#notifyUsers").val());
    let notesAsPrivate = parseInt(getFieldsValue('notesAsPrivate2'));

    console.log({
        file: 'popup.js',
        func: 'showAndHideNotifyUsers',
        notifyUsers: notifyUsers,
        notesAsPrivate: notesAsPrivate,
    });

    if(notesAsPrivate) {
        showClientInfo(notesAsPrivate);
    }

    if (notifyUsers === 1) {
        $(".notifyUsers").removeClass('hide');
    } else {
        $(".notifyUsers").addClass('hide');
    }
}

jQuery.validator.addMethod(
    "phoneNumberValidation",
    function (value, element) {
        if (this.optional(element)) {
            return true;                          // return true on optional element
        }
        str1 = value.replace(/[^0-9]/g, '');
        console.log(str1.length);
        if (str1.length == 10 || str1.length == 14) {
            return true;
        } else {
            return false;
        }
    },
    "Please Enter Valid Phone Number"
);

jQuery.validator.addMethod(
    "zipCodeValidation",
    function (value, element) {
        if (this.optional(element)) {
            return true;                          // return true on optional element
        }
        str1 = value.replace(/[^0-9]/g, '');
        console.log(str1.length);
        if (str1.length == 5) {
            return true;
        } else {
            return false;
        }
    },
    "Please Enter Valid Zip Code"
);
//Example: editRep2Info.php

jQuery.validator.addMethod(
    "dateFormat",
    function (value, element) {
        var check = false;
        var re = /^\d{1,2}\/\d{1,2}\/\d{4}$/;
        if (re.test(value)) {
            var adata = value.split('/');
            var mm = parseInt(adata[0], 10);
            var dd = parseInt(adata[1], 10);
            var yyyy = parseInt(adata[2], 10);
            var xdata = new Date(yyyy, mm - 1, dd);
            if ((xdata.getFullYear() === yyyy) && (xdata.getMonth() === mm - 1) && (xdata.getDate() === dd)) {
                check = true;
            } else {
                check = false;
            }
        } else {
            check = false;
        }
        return this.optional(element) || check;
    },
    "Wrong date format"
);

/*console.log('sas');
import {FormInputMask1} from 'form-input-mask.min.js';*/
var url = siteUrl + "assets/js/form-input-mask.min.js";
$.getScript(url, function () {

});

function getPCServiceTypes(moduleCode, PCID) {
    console.log({
        func: 'getPCServiceTypes',
    });
    var tempUsedLoanPgmJsonObje = '';
    var url = "/JQFiles/getPCServiceTypes.php";
    var qstr = "PCID=" + PCID + "&moduleCode=" + moduleCode + "&getInternalLoanPrograms=1";
    try {
        xmlDoc = getXMLDoc(url, qstr);
    } catch (e) {
    }
    var len = 0;
    try {
        servicesRequested = xmlDoc.getElementsByTagName("serList");
        len = servicesRequested.length;
    } catch (e) {
    }
    br = '';

    try {
        tempUsedLoanPgm = $('#tempUsedLoanPgm').val();
        tempUsedLoanPgmJsonObje = JSON.parse(tempUsedLoanPgm);
    } catch (e) {

    }

    for (var ae = 0; ae < len; ae++) {
        var categoryName = "", catKey = 0, chk = '';

        try {
            catKey = servicesRequested[ae].getElementsByTagName("catKey")[0].childNodes[0].nodeValue;
        } catch (e) {
        }
        try {
            categoryName = servicesRequested[ae].getElementsByTagName("categoryName")[0].childNodes[0].nodeValue;
        } catch (e) {
        }
        try {
            chk = servicesRequested[ae].getElementsByTagName("chk")[0].childNodes[0].nodeValue;
        } catch (e) {
        }
        try {
            internalLoanProgram = servicesRequested[ae].getElementsByTagName("internalLoanProgram")[0].childNodes[0].nodeValue;
        } catch (e) {
        }
        if (!tempUsedLoanPgmJsonObje.includes(catKey)) {
            if (internalLoanProgram == '1') {
                optionColor = 'style="color: red;"';
            } else {
                optionColor = '';
            }
            br += '<option value="' + catKey + '" ' + chk + ' ' + optionColor + '>' + categoryName + '</option>';
        }
    }
    $('#loanPgm').empty();
    $('#loanPgm').append(br).trigger("chosen:updated");
}


/*
var urlMaks = "form-input-mask.min.js";
$.getScript(urlMaks, function(){
    $(document).ready(function(){
        FormInputMask.init();
    });
});
*/
