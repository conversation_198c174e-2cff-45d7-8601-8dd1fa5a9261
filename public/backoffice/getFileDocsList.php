<?php

/**
 * Description    : Sorting the doc list for Docs tab
 * developer    : Venkatesh
 * Date        : May 23, 2017
 **/

use models\composite\oChecklist\getChecklistNameID;
use models\composite\oFileDoc\getFileDocsList;
use models\composite\oFileDoc\getNewDocumentTypeRequiredBy;
use models\cypher;
use models\Request;
use models\standard\Strings;
use models\standard\UserAccess;

session_start();
require '../includes/util.php';


require CONST_BO_PATH . 'initPageVariables.php';
require CONST_BO_PATH . 'getPageVariables.php';

UserAccess::checkReferrerPgs(['url' => 'LMRequest.php,HMLOWebForm.php']);
//CheckAdminUse();

$LMRId = 0;
$tempDocInfoArray = [];
$orderBy = 'desc';
$sortOpt = 'uploadedDate';
$docsKeysArray = [];
$oldFPCID = 0;
$empIdArray = [];
$branchIdArray = [];
$agentIdArray = [];
$clientIdArray = [];
$allowToEdit = 1;
$recordDate = '';
$resFileSizeInfo = [];
$binderDocsKeysArray = [];
$fileModules = [];
$trustDocKeyArray = [];
$resultInfoArray = [];
$fileInfoArray = [];
$clientName = '';
$borrowerName = '';
$borrowerLName = '';
$activeTab = '';
$category = '';


if (isset($_REQUEST['LMRId'])) $LMRId = intval(cypher::myDecryption(Request::GetClean('LMRId')));
if (isset($_REQUEST['sortOpt'])) $sortOpt = trim($_REQUEST['sortOpt']);
if (isset($_REQUEST['orderBy'])) $orderBy = trim($_REQUEST['orderBy']);
if (isset($_REQUEST['PCID'])) $oldFPCID = intval(cypher::myDecryption(Request::GetClean('PCID')));
if (isset($_REQUEST['allowToEdit'])) $allowToEdit = cypher::myDecryption($_REQUEST['allowToEdit']);
if (isset($_REQUEST['recordDate'])) $recordDate = cypher::myDecryption($_REQUEST['recordDate']);
if (isset($_REQUEST['activeTab'])) $activeTab = trim($_REQUEST['activeTab']);
if (isset($_REQUEST['category'])) $category = trim($_REQUEST['category']);
$activeStatus = $_REQUEST['activeStatus'] ?? '1'; //Default::Get active docs
$inArray = [
    'LMRId' => $LMRId
    , 'sortOpt' => $sortOpt
    , 'orderBy' => $orderBy
    , 'activeTab' => $activeTab
    , 'category' => $category
    , 'activeStatus' => $activeStatus
];
if ($LMRId > 0) {
    $resultInfoArray = getFileDocsList::getReport($inArray);
}

if (count($resultInfoArray) > 0) {
    $tempDocInfoArray = $resultInfoArray['docListInfo'];
    $fileInfoArray = $resultInfoArray['fileInfo'][0];
}

if (count($fileInfoArray) > 0) {
    $oldFPCID = $fileInfoArray['oldFPCID'] > 0 ? $fileInfoArray['oldFPCID'] : $fileInfoArray['PCID'];
    $borrowerName = $fileInfoArray['borrowerFName'];
    $borrowerLName = $fileInfoArray['borrowerLName'];
    $recordDate = $fileInfoArray['recordDate'];
}
$clientName = ucwords($borrowerName . ' ' . $borrowerLName);

require 'getAllFileDocsInfoFromRemote.php';

$ip['fileID'] = $LMRId;
$docCheckListName = getChecklistNameID::getReport($ip);
$ip['LMRId'] = $LMRId;
$otherDocumentRequiredByList = getNewDocumentTypeRequiredBy::getReport($ip);

for ($i = 0; $i < count($tempDocInfoArray); $i++) {
    $userId = 0;
    $userType = '';
    $userId = $tempDocInfoArray[$i]['uploadedBy'];
    $userType = $tempDocInfoArray[$i]['uploadingUserType'];

    if ($userType == 'Processor' || $userType == 'Employee') {
        $empIdArray[] = $userId;
    }
    if ($userType == 'LMR Executive' || $userType == 'Branch') {
        $branchIdArray[] = $userId;
    }
    if ($userType == 'Broker' || $userType == 'Agent') {
        $agentIdArray[] = $userId;
    }
    if ($userType == 'Client' && $userId > 0) {
        $clientIdArray[] = $userId;
    }
    if ($tempDocInfoArray[$i]['docCategory'] == 'Checklist Items') {
        $tempDocInfoArray[$i]['docChecklistName'] = $docCheckListName[$tempDocInfoArray[$i]['docID']];
    } elseif (in_array($tempDocInfoArray[$i]['docCategory'], ['Appraisal1', 'Appraisal2', 'BPO1', 'BPO2', 'BPO3', 'Title Report', 'Property Insurance Coverage1', 'Property Insurance Coverage2', 'Property Insurance Coverage3', 'Property Insurance Coverage4', 'Property Insurance Coverage5', 'Property Insurance Coverage', 'AVM1', 'AVM2', 'AVM3'])) {
        $tempDocInfoArray[$i]['docChecklistName'] = $tempDocInfoArray[$i]['docCategory']; //"Other new";
        $tempDocInfoArray[$i]['docCategory'] = 'Other';
        /*  $tempDocInfoArray[$i]["docChecklistName"] = $tempDocInfoArray[$i]["docCategory"]; //"Other new";
          $tempDocInfoArray[$i]["docCategory"] = "Other";*/
    } else {
        $tempDocInfoArray[$i]['docChecklistName']['doctypeName'] = $tempDocInfoArray[$i]['docCategory']; //"Other new";
        $tempDocInfoArray[$i]['docChecklistName']['docCat'] = 'file'; //"Other new";
        $tempDocInfoArray[$i]['docChecklistName']['requiredByList'] = $otherDocumentRequiredByList[$tempDocInfoArray[$i]['docCategory']]['requiredByList'];

        // Set custom category name for Budget Docs
        if ($tempDocInfoArray[$i]['docCategory'] == 'Budget Docs') {
            $tempDocInfoArray[$i]['docChecklistName']['categoryName'] = 'Draw Management';
        }

        $tempDocInfoArray[$i]['docCategory'] = 'Other';
    }
}


$empInfoArray = [];
$branchInfoArray = [];
$agentInfoArray = [];
$clientInfoArray = [];
if (count($empIdArray) > 0) {
    $ip = ['empId' => implode(',', $empIdArray)];
    $empInfoArray = \models\composite\oEmployee\getMyDetails::getReport($ip);
}
if (count($branchIdArray) > 0) {
    $ip = ['executiveId' => implode(',', $branchIdArray)];
    $branchInfoArray = \models\composite\oBranch\getMyDetails::getReport($ip);
}
if (count($agentIdArray) > 0) {
    $ip = ['agentId' => implode(',', $agentIdArray)];
    $agentInfoArray = \models\composite\oBroker\getMyDetails::getReport($ip);
}
if (count($clientIdArray) > 0) {
    $ip = ['clientId' => implode(',', $clientIdArray)];
    $clientInfoArray = \models\composite\oClient\getMyDetails::getReport($ip);
}

if ($activeTab == 'DOC' || $activeTab == 'CFPB') {
    require 'fileUploadDocsListTable.php';
} else if ($activeTab == 'PI' && $category == 'HMLO CMA Report') {
    require 'fileHMLOCMAReportUploadTable.php';
} else {
    require 'filePictureOfPropTable.php';
}

