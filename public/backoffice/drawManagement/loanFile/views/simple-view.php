<?php

use models\standard\Strings;
use models\lendingwise\tblFileSimpleDrawRequests;
use models\Controllers\backoffice\LMRequest;

Strings::includeMyScript(['/backoffice/drawManagement/js/simpleDraw.js']);

$drawsData = tblFileSimpleDrawRequests::GetAll(['LMRId' => $LMRId]);
$drawsData = $drawsData ?? [];

LMRequest::setLMRId($LMRId);
$fileHMLONewLoanInfo = LMRequest::myFileInfo()->getFileHMLONewLoanInfo();
$rehabPercentFinanced = $fileHMLONewLoanInfo ? Strings::replaceCommaValues($fileHMLONewLoanInfo->rehabCostPercentageFinanced) : 0;
$drawFee = $templateSettings->drawFee ?? 0;
?>

<?php require __DIR__ . '/../drawSummary.php'; ?>

<div class="card card-body p-0">
    <div class="card card-custom card-stretch d-flex p-0 drawHistoryCard">
        <div class="card-header card-header-tabs-line bg-gray-100">
            <div class="card-title">
                <h3 class="card-label">
                    Draw Request History
                </h3>
            </div>
            <div class="card-toolbar">
                <span class="btn btn-sm btn-success btn-text-primary btn-icon ml-2 tooltipClass cursor-pointer add-row-btn"
                    title="Click to add new row">
                    <i class="icon-md fas fa-plus"></i>
                </span>
                <a href="javascript:void(0);"
                    class="tooltipClass btn  btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass"
                    data-card-tool="toggle" data-section="drawHistoryCard" data-toggle="tooltip" data-placement="top"
                    title="" data-original-title="Toggle Card">
                    <i class="ki ki-arrow-down icon-nm"></i>
                </a>
            </div>
        </div>

        <div class="card-body p-2">
            <div class="row">
                <div class="col-12">
                    <div class="work-table">
                        <table class="table table-striped table-hover mb-0" id="simpleDrawTable">
                            <thead>
                                <tr>
                                    <th style="width: 12%;">Status</th>
                                    <th style="width: 12%;">Submission Date</th>
                                    <th style="width: 12%;">Amount Requested</th>
                                    <th style="width: 12%;">Amount Approved</th>
                                    <th style="width: 10%;">Rehab% Financed</th>
                                    <th style="width: 12%;">Draw Fee</th>
                                    <th style="width: 12%;">Wire Amount</th>
                                    <th style="width: 12%;">Wire Sent Date</th>
                                    <th style="width: 6%;">Actions</th>
                                </tr>
                            </thead>
                            <tbody id="simpleDrawTableBody">
                                <?php if (!empty($drawsData)): ?>
                                    <?php foreach ($drawsData as $row): ?>
                                    <tr data-row-id="<?= $row->id ?? 'new' ?>">
                                        <td>
                                            <select class="form-control" name="status" data-field="status">
                                                <option value="approved" <?= ($row->status ?? '') === 'approved' ? 'selected' : '' ?>>Approved</option>
                                                <option value="pending" <?= ($row->status ?? '') === 'pending' ? 'selected' : '' ?>>Pending</option>
                                                <option value="rejected" <?= ($row->status ?? '') === 'rejected' ? 'selected' : '' ?>>Rejected</option>
                                            </select>
                                        </td>
                                        <td>
                                            <input type="text"
                                                class="form-control dateClass"
                                                name="submittedAt"
                                                data-field="submittedAt"
                                                value="<?= isset($row->submittedAt) ? date('m/d/Y', strtotime($row->submittedAt)) : '' ?>"
                                                placeholder="mm/dd/yyyy">
                                        </td>
                                        <td>
                                            <div class="input-group">
                                                <div class="input-group-prepend">
                                                    <span class="input-group-text">$</span>
                                                </div>
                                                <input type="number"
                                                    class="form-control"
                                                    name="requestedAmount"
                                                    data-field="requestedAmount"
                                                    <?= ($row->requestedAmount ?? 0) > 0 ? 'value="' . ($row->requestedAmount ?? 0) . '"' : '' ?>
                                                    min="0"
                                                    placeholder="0.00">
                                            </div>
                                        </td>
                                        <td>
                                            <div class="input-group">
                                                <div class="input-group-prepend">
                                                    <span class="input-group-text">$</span>
                                                </div>
                                                <input type="number"
                                                    class="form-control"
                                                    name="approvedAmount"
                                                    data-field="approvedAmount"
                                                    <?= ($row->approvedAmount ?? 0) > 0 ? 'value="' . ($row->approvedAmount ?? 0) . '"' : '' ?>
                                                    min="0"
                                                    placeholder="0.00">
                                            </div>
                                        </td>
                                        <td>
                                            <div class="input-group">
                                                <div class="input-group-prepend">
                                                    <span class="input-group-text">%</span>
                                                </div>
                                                <input type="number"
                                                    class="form-control"
                                                    name="rehabPercentFinanced"
                                                    data-field="rehabPercentFinanced"
                                                    <?= ($row->rehabPercentFinanced ?? 0) > 0 ? 'value="' . ($row->rehabPercentFinanced ?? 0) . '"' : '' ?>
                                                    min="0"
                                                    placeholder="0.00">
                                            </div>
                                        </td>
                                        <td>
                                            <div class="input-group">
                                                <div class="input-group-prepend">
                                                    <span class="input-group-text">$</span>
                                                </div>
                                                <input type="number"
                                                    class="form-control"
                                                    name="drawFee"
                                                    data-field="drawFee"
                                                    <?= ($row->drawFee ?? 0) > 0 ? 'value="' . ($row->drawFee ?? 0) . '"' : '' ?>
                                                    min="0"
                                                    placeholder="0.00">
                                            </div>
                                        </td>
                                        <td>
                                            <div class="input-group">
                                                <div class="input-group-prepend">
                                                    <span class="input-group-text">$</span>
                                                </div>
                                                <input type="number"
                                                    class="form-control"
                                                    name="wireAmount"
                                                    data-field="wireAmount"
                                                    value="<?= $row->wireAmount ?? '' ?>"
                                                    min="0"
                                                    placeholder="0.00"
                                                    disabled>
                                            </div>
                                        </td>
                                        <td>
                                            <input type="text"
                                                class="form-control dateClass"
                                                name="wireSentDate"
                                                data-field="wireSentDate"
                                                value="<?= isset($row->wireSentDate) ? date('m/d/Y', strtotime($row->wireSentDate)) : '' ?>"
                                                placeholder="mm/dd/yyyy">
                                        </td>
                                        <td>
                                            <span class="btn btn-sm btn-danger btn-text-primary btn-icon cursor-pointer remove-row-btn"
                                                title="Click to remove row">
                                                <i class="icon-md fas fa-minus-circle"></i>
                                            </span>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <!-- Empty row will be added by JavaScript using template -->
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Template for new draw request rows -->
            <template id="drawRequestRowTemplate">
                <tr data-row-id="new">
                    <td>
                        <select class="form-control" name="status" data-field="status">
                            <option value="approved" selected>Approved</option>
                            <option value="pending">Pending</option>
                            <option value="rejected">Rejected</option>
                        </select>
                    </td>
                    <td>
                        <input type="text"
                            class="form-control dateClass"
                            name="submittedAt"
                            data-field="submittedAt"
                            placeholder="mm/dd/yyyy">
                    </td>
                    <td>
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">$</span>
                            </div>
                            <input type="number"
                                class="form-control"
                                name="requestedAmount"
                                data-field="requestedAmount"
                                min="0"
                                placeholder="0.00">
                        </div>
                    </td>
                    <td>
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">$</span>
                            </div>
                            <input type="number"
                                class="form-control"
                                name="approvedAmount"
                                data-field="approvedAmount"
                                min="0"
                                placeholder="0.00">
                        </div>
                    </td>
                    <td>
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">%</span>
                            </div>
                            <input type="number"
                                class="form-control"
                                name="rehabPercentFinanced"
                                data-field="rehabPercentFinanced"
                                min="0"
                                <?= $rehabPercentFinanced > 0 ? 'value="' . $rehabPercentFinanced . '"' : '' ?>
                                placeholder="0.00">
                        </div>
                    </td>
                    <td>
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">$</span>
                            </div>
                            <input type="number"
                                class="form-control"
                                name="drawFee"
                                data-field="drawFee"
                                min="0"
                                <?= $drawFee > 0 ? 'value="' . $drawFee . '"' : '' ?>
                                placeholder="0.00">
                        </div>
                    </td>
                    <td>
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">$</span>
                            </div>
                            <input type="number"
                                class="form-control"
                                name="wireAmount"
                                data-field="wireAmount"
                                min="0"
                                placeholder="0.00"
                                disabled>
                        </div>
                    </td>
                    <td>
                        <input type="text"
                            class="form-control dateClass"
                            name="wireSentDate"
                            data-field="wireSentDate"
                            placeholder="mm/dd/yyyy">
                    </td>
                    <td>
                        <span class="btn btn-sm btn-danger btn-text-primary btn-icon tooltipClass cursor-pointer remove-row-btn"
                            title="Click to remove row">
                            <i class="icon-md fas fa-minus-circle"></i>
                        </span>
                    </td>
                </tr>
            </template>
            <div class="row mt-10">
                <div class="col-12 text-center">
                    <button type="button" class="btn btn-primary save-draw-requests-btn">Save</button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    $(document).ready(function() {
        if (typeof SimpleDrawManager !== 'undefined') {
            const config = {
                LMRId: <?= $LMRId ?>
            };
            SimpleDrawManager.init(config);
        } else {
            console.error('SimpleDrawManager class not found. Make sure the script is loaded.');
        }
    });
</script>
