<?php
use models\composite\oDrawManagement\DrawRequest;
?>

<table class="table table-hover table-bordered table-vertical-center">
    <thead>
        <tr>
            <th style="width: 12%;">Line Item</th>
            <th style="width: 9%;">Total Budget</th>
            <th style="width: 9%;">Completed Renovations</th>
            <th style="width: 7%;">% Completed</th>
            <th style="width: 7%;">Prv. Disbursed</th>
            <th style="width: 19%;">Disbursement This Draw</th>
            <th style="width: 15%;">Requested This Draw</th>
            <th style="width: 4%;">Borrower Notes</th>
            <th style="width: 4%;">Lender Notes</th>
            <th style="width: 10%;" class="hide col-reject-reason">Reject Reason</th>
            <th style="width: 4%;">Docs</th>
        </tr>
    </thead>
    <tbody>
        <?php if (!empty($categoriesData)): ?>
            <?php foreach ($categoriesData as $category): ?>
                <?php if (!empty($category->getAllLineItems())): ?>
                    <tr class="category-header">
                        <td colspan="11">
                            <?= htmlspecialchars(strtoupper($category->categoryName)) ?>
                            <?php if (!empty($category->description)): ?>
                            <i class="fa fa-info-circle text-primary tooltipClass ml-2"
                                title="<?= htmlspecialchars($category->description) ?>"></i>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <?php if (!empty($category->getAllLineItems())): ?>
                        <?php foreach ($category->getAllLineItems() as $lineItem): ?>
                            <tr class="line-item">
                                <td>
                                    <?= htmlspecialchars($lineItem->name) ?>
                                    <?php if (!empty($lineItem->description)): ?>
                                        <i class="fa fa-info-circle text-primary tooltipClass ml-2" title="<?= htmlspecialchars($lineItem->description) ?>"></i>
                                    <?php endif; ?>
                                </td>
                                <td>$<?= $lineItem->cost ?></td>
                                <td>$<?= $lineItem->completedAmount ?></td>
                                <td>
                                    <span class="badge percentage"><?= round($lineItem->completedPercent) ?>%</span>
                                </td>
                                <td>$<?= number_format($lineItem->disbursedAmount, 2) ?></td>
                                <td>
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text bold">%</span>
                                        </div>
                                        <input type="number"
                                                name="requestedPercent"
                                                class="form-control requested-percent mandatory"
                                                min="0"
                                                step="100"
                                                data-line-item-id="<?= $lineItem->id ?>"
                                                data-cost="<?= $lineItem->cost ?>"
                                                data-completed-amount="<?= $lineItem->completedAmount ?>"
                                                data-completed-percent="<?= round($lineItem->completedPercent, 2) ?>"
                                                placeholder="0"
                                                style="max-width: 100px;"
                                                <?= $disableAction ? 'disabled' : '' ?>/>
                                    <div class="input-group-prepend">
                                        <span class="input-group-text bold">$</span>
                                    </div>
                                    <input type="number"
                                            name="requestedAmount"
                                            class="form-control requested-amount mandatory"
                                            min="0"
                                            step="100"
                                            data-line-item-id="<?= $lineItem->id ?>"
                                            data-cost="<?= $lineItem->cost ?>"
                                            data-completed-amount="<?= $lineItem->completedAmount ?>"
                                            data-completed-percent="<?= round($lineItem->completedPercent, 2) ?>"
                                            placeholder="0.00"
                                            <?= $disableAction ? 'disabled' : '' ?>/>
                                    </div>
                                    <div class="validation-message text-danger small mt-1" style="display: none;"></div>
                                </td>
                                <td>
                                    <?php if($requestData->status !== DrawRequest::STATUS_APPROVED): ?>
                                    <span class="h6">
                                        <?= $lineItem->cost > 0 ? round($lineItem->requestedAmount / $lineItem->cost * 100) : 0?>% <span class= "text-muted">|</span> $<?= number_format($lineItem->requestedAmount, 2) ?>
                                    </span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <button class="btn note-btn btn-sm" type="button">
                                        <i class="icon-md fas fa-comment-medical fa-lg tooltipClass <?= !empty($lineItem->notes) ? 'text-primary' : 'text-muted' ?>"
                                            data-original-title="<?= htmlspecialchars($lineItem->notes) ?>">
                                        </i>
                                        <input type="hidden" name="notes" value="<?= htmlspecialchars($lineItem->notes) ?>">
                                    </button>
                                </td>
                                <td>
                                    <button class="btn lender-note-btn btn-sm" type="button"
                                        data-line-item-id="<?= $lineItem->id ?>">
                                        <i class="icon-md fas fa-comment-medical fa-lg tooltipClass <?= !empty($lineItem->lenderNotes) ? 'text-primary' : 'text-muted' ?>"
                                            data-original-title="<?= htmlspecialchars($lineItem->lenderNotes) ?>">
                                        </i>
                                    </button>
                                </td>
                                <td class="hide col-reject-reason">
                                    <select class="form-control input-sm" name="rejectReason">
                                        <option <?= $lineItem->rejectReason === '' ? 'selected' : ''; ?> value="">-- None --</option>
                                        <option <?= $lineItem->rejectReason === 'Revise Budget' ? 'selected' : ''; ?> value="Revise Budget">Revise Budget</option>
                                        <option <?= $lineItem->rejectReason === 'Line Item not covered' ? 'selected' : ''; ?> value="Line Item not covered">Line Item not covered</option>
                                        <option <?= $lineItem->rejectReason === 'Other(See Notes)' ? 'selected' : ''; ?> value="Other(See Notes)">Other(See Notes)</option>
                                    </select>
                                </td>
                                <td>
                                    <a href="javascript:void(0);" class="view-docs-btn btn btn-sm btn-light btn-text-primary btn-hover-primary btn-icon tooltipClass position-relative"
                                        data-line-item-id="<?= $lineItem->id ?>" data-docs-count="<?= $lineItem->docCount ?>"
                                        data-original-title="Uploaded Docs: <?= $lineItem->docCount ?>">
                                        <i class="fas fa-folder-open <?= $lineItem->hasDocs ? 'text-success' : 'text-muted' ?>"></i>
                                        <?php if ($lineItem->docCount > 0): ?>
                                            <span class="position-absolute badge rounded-pill bg-danger text-white doc-count-badge">
                                                <?= $lineItem->docCount ?>
                                            </span>
                                        <?php endif; ?>
                                    </a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                <?php endif; ?>
            <?php endforeach; ?>
        <?php else: ?>
        <tr>
            <td colspan="11" class="text-center text-muted py-4">
                No draw request data available.
            </td>
        </tr>
        <?php endif; ?>
    </tbody>
</table>
