window.SimpleDrawManager = window.SimpleDrawManager || {};

SimpleDrawManager = {
    config: {
        templateId: 'drawRequestRowTemplate',
        tableBodyId: 'simpleDrawTableBody',
        apiUrl: '/backoffice/api_v2/draw_management/SimpleDrawRequests/',
        LMRId: null,
        idsToDelete: []
    },

    selectors: {
        tableBody: '#simpleDrawTableBody',
        saveButton: '.save-draw-requests-btn',
        addRowButton: '.add-row-btn',
        removeRowButton: '.remove-row-btn'
    },

    createNewRowFromTemplate() {
        const template = document.getElementById(this.config.templateId);
        const $newRow = $(template.content.cloneNode(true)).find('tr');

        return $newRow;
    },

    addSimpleDrawRow() {
        const $tableBody = $(this.selectors.tableBody);
        const $newRow = this.createNewRowFromTemplate();

        $tableBody.append($newRow);

        $newRow.find('.dateClass').each(function() {
            dateClass.init($(this));
        });

        this.bindRowEvents($newRow);
    },

    bindRowEvents($row) {
        const $approvedAmountInput = $row.find('[data-field="approvedAmount"]');
        const $drawFeeInput = $row.find('[data-field="drawFee"]');
        const $rehabPercentFinancedInput = $row.find('[data-field="rehabPercentFinanced"]');

        if ($approvedAmountInput.length && $drawFeeInput.length) {
            $approvedAmountInput.on('input', () => {
                this.calculateWireAmount($row);
            });

            $drawFeeInput.on('input', () => {
                this.calculateWireAmount($row);
            });

            $rehabPercentFinancedInput.on('input change', () => {
                this.calculateWireAmount($row);
            });

            if ($drawFeeInput.val()) {
                this.calculateWireAmount($row);
            }
        }
    },

    removeSimpleDrawRow(element) {
        const $row = $(element).closest('tr');
        const $tableBody = $(this.selectors.tableBody);
        const rowId = $row.attr('data-row-id');

        if (rowId && rowId !== 'new') {
            this.config.idsToDelete.push(rowId);
            $row.remove();
        } else {
            this.handleNewRowRemoval($row, $tableBody);
        }
    },

    handleNewRowRemoval($row, $tableBody) {
        if ($tableBody.children().length > 1) {
            $row.remove();
        } else {
            $row.find('input, select').val('');
            $row.find('select[name="status"]').val('pending');
        }
    },

    saveSimpleDrawRequests() {
        const $tableBody = $(this.selectors.tableBody);
        const $rows = $tableBody.find('tr');
        const drawRequests = [];
        let hasErrors = false;

        $rows.each((index, element) => {
            const $row = $(element);
            const rowData = this.extractRowData($row);

            if (!this.validateRowData(rowData, index)) {
                hasErrors = true;
                return false;
            }

            if (rowData.status !== 'pending' || rowData.submittedAt || parseFloat(rowData.requestedAmount) > 0) {
                drawRequests.push(rowData);
            }
        });

        if (hasErrors) {
            return;
        }

        this.submitDrawRequests(drawRequests);
    },

    extractRowData($row) {
        return {
            id: $row.attr('data-row-id'),
            status: $row.find('[data-field="status"]').val(),
            submittedAt: $row.find('[data-field="submittedAt"]').val(),
            requestedAmount: $row.find('[data-field="requestedAmount"]').val(),
            approvedAmount: $row.find('[data-field="approvedAmount"]').val(),
            rehabPercentFinanced: $row.find('[data-field="rehabPercentFinanced"]').val(),
            drawFee: $row.find('[data-field="drawFee"]').val(),
            wireAmount: $row.find('[data-field="wireAmount"]').val(),
            wireSentDate: $row.find('[data-field="wireSentDate"]').val()
        };
    },

    validateRowData(rowData, index) {
        if (!rowData.status || !rowData.submittedAt || !rowData.requestedAmount) {
            toastrNotification(`Row ${index + 1}: Status, Submission Date, and Amount Requested are required`, 'error');
            return false;
        }
        return true;
    },

    submitDrawRequests(drawRequests) {
        const requestData = {
            LMRId: this.config.LMRId,
            drawRequests: drawRequests,
            idsToDelete: this.config.idsToDelete
        };

        const $saveBtn = $(this.selectors.saveButton);
        const originalText = $saveBtn.text();

        this.toggleSaveButton($saveBtn, true, 'Saving...');

        $.ajax({
            url: this.config.apiUrl,
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(requestData),
            success: (response) => {
                if (response.success) {
                    toastrNotification('Saved successfully!', 'success');
                    setTimeout(() => {
                        location.reload();
                    }, 1500);
                } else {
                    toastrNotification('Error: ' + (response.message || 'Failed to save'), 'error');
                }
            },
            error: (xhr, status, error) => {
                console.error('AJAX Error:', error);
                let errorMsg = 'An error occurred while saving data. Please try again.';

                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMsg = xhr.responseJSON.message;
                } else if (xhr.responseText) {
                    try {
                        const errorResponse = JSON.parse(xhr.responseText);
                        errorMsg = errorResponse.message || errorMsg;
                    } catch (e) {
                    }
                }

                toastrNotification(errorMsg, 'error');
            },
            complete: () => {
                this.toggleSaveButton($saveBtn, false, originalText);
            }
        });
    },

    toggleSaveButton($button, disabled, text) {
        $button.prop('disabled', disabled);
        $button.text(text);
    },

    calculateWireAmount($row) {
        const $approvedAmountInput = $row.find('[data-field="approvedAmount"]');
        const $drawFeeInput = $row.find('[data-field="drawFee"]');
        const $wireAmountInput = $row.find('[data-field="wireAmount"]');
        const rehabPercentFinanced = parseFloat($row.find('[data-field="rehabPercentFinanced"]').val()) / 100;

        const approvedAmount = parseFloat($approvedAmountInput.val()) || 0;
        const drawFee = parseFloat($drawFeeInput.val()) || 0;

        const wireAmount = (approvedAmount * rehabPercentFinanced) - drawFee;

        $wireAmountInput.val(Math.max(0, wireAmount).toFixed(2));
    },

    setupWireAmountCalculation() {
        $(this.selectors.tableBody + ' tr').each((index, element) => {
            const $row = $(element);
            this.bindRowEvents($row);
        });
    },

    setupEventHandlers() {
        $(document).off('click.simpleDrawManager');

        $(document).on('click.simpleDrawManager', this.selectors.addRowButton, (e) => {
            e.preventDefault();
            this.addSimpleDrawRow();
        });

        $(document).on('click.simpleDrawManager', this.selectors.tableBody + ' ' + this.selectors.removeRowButton, (e) => {
            e.preventDefault();
            this.removeSimpleDrawRow(e.currentTarget);
        });

        $(document).on('click.simpleDrawManager', this.selectors.saveButton, (e) => {
            e.preventDefault();
            this.saveSimpleDrawRequests();
        });
    },

    init(settings) {
        this.config.LMRId = settings.LMRId;

        this.setupEventHandlers();

        $('.dateClass').each(function() {
            dateClass.init($(this));
        });

        const $tableBody = $(this.selectors.tableBody);
        if ($tableBody.find('tr').length === 0) {
            this.addSimpleDrawRow();
        }

        this.setupWireAmountCalculation();
    }
};


