<?php

namespace pages\backoffice\api_v2\draw_management\Docs;

use models\composite\oFileDoc\saveFileDocument;
use models\constants\gl\glMimeTypes;
use models\cypher;
use models\composite\oDrawManagement\BorrowerDrawLineItem;
use models\PageVariables;
use models\standard\Strings;
use models\UploadServer;
use models\Log;
use pages\backoffice\api_v2\draw_management\base\DrawManagementApiBase;
use models\lendingwise\tblDrawRequestLineItems;
use models\lendingwise\tblLMRFileDocs;
use models\Database2;
use models\composite\oFileDoc\deleteFileDocs;
use models\composite\oDrawManagement\SowTemplateManager;
use models\composite\oDrawManagement\DrawStageHelper;

/**
 * API endpoint for handling documents related to draw request line items
 *
 * @package pages\backoffice\api_v2\draw_management\line_items\docs
 */
class Docs extends DrawManagementApiBase
{

    public static ?int $LMRId = null;
    public static ?int $lineItemId = null;
    public static ?BorrowerDrawLineItem $borrowerLineItem = null;
    public static ?string $docName = null;
    public static ?string $fileExtension = null;
    public static ?int $docID = null;
    public static ?string $uploaderType = null;
    public static ?object $loanFile = null;

    public static ?int $userNumber = null;
    public static ?string $userName = null;
    public static ?string $userGroup = null;

    private const MAX_UPLOADS_PER_LINE_ITEM = 50;

    /**
     * Handle POST requests for document upload
     * @return void
     */
    public static function Post(): void
    {
        parent::Init();

        static::executeWithErrorHandling(function() {
            static::initializeVariables();

            if (!static::$lineItemId || !static::$LMRId) {
                static::sendErrorResponse('Missing required parameters: lineItemId and LMRId');
                return;
            }

            if (!static::validateLineItem()) {
                return;
            }

            static::$loanFile = static::validateLoanFile(static::$LMRId);

            if (!static::validateFileUpload()) {
                return;
            }

            static::saveUploadedDocument();

            static::sendSuccessResponse([
                'docID' => static::$docID,
                'fileName' => $_FILES['file']['name'],
                'uploaderType' => ucwords(static::$uploaderType),
                'uploadedDate' => date('Y-m-d H:i:s')
            ], 'Document uploaded successfully');

        }, 'Document upload failed');
    }

    /**
     * Handle GET requests for retrieving line item documents
     */
    public static function Get(): void
    {
        parent::Init();

        static::executeWithErrorHandling(function() {
            $lineItemId = $_GET['lineItemId'] ?? null;

            if (!$lineItemId) {
                static::sendErrorResponse('Missing required parameter: lineItemId');
                return;
            }

            $lineItemRecord = tblDrawRequestLineItems::Get(['id' => $lineItemId]);
            if (!$lineItemRecord) {
                static::sendErrorResponse('Line item not found');
                return;
            }

            $lineItem = new BorrowerDrawLineItem($lineItemRecord);
            $documents = $lineItem->getDocuments();

            foreach ($documents as &$document) {
                $document['viewUrl'] = BorrowerDrawLineItem::generateDocumentViewUrl($document);
                $document['uploaderType'] = ucwords($document['uploaderType']);
                $document['stageUploaded'] = $document['uploadStage'] ?? 'Unknown';
            }

            static::sendSuccessResponse([
                'documents' => $documents,
                'count' => count($documents)
            ], 'Documents retrieved successfully');

        }, 'Failed to retrieve documents');
    }

    /**
     * Handle DELETE requests for removing line item documents
     */
    public static function Delete(): void
    {
        parent::Init();

        static::executeWithErrorHandling(function() {

            static::initializeDeleteVariables();

            if (!static::$docID) {
                static::sendErrorResponse('Missing required parameter: docID');
                return;
            }

            if (!static::validateDeletePermissions()) {
                return;
            }

            static::performDocumentDeletion();

            static::sendSuccessResponse([
                'docID' => static::$docID
            ], 'Document deleted successfully');

        }, 'Document deletion failed');
    }

    /**
     * Initialize variables from request data
     * @return void
     */
    private static function initializeVariables(): void
    {
        static::$lineItemId = $_POST['lineItemId'] ?? null;
        static::$LMRId = static::getLMRId($_POST['LMRId'] ?? null);

        static::$userNumber = Strings::GetSess('userNumber');
        static::$userName = Strings::GetSess('firstName') . ' ' . Strings::GetSess('lastName');
        static::$userGroup = Strings::GetSess('userGroup');

        static::$uploaderType = $_POST['uploaderType'] ?? 'borrower';
    }

    /**
     * Validate line item exists and create BorrowerDrawLineItem instance
     * @return bool
     */
    private static function validateLineItem(): bool
    {
        $lineItemRecord = tblDrawRequestLineItems::Get(['id' => static::$lineItemId]);
        if (!$lineItemRecord) {
            static::sendErrorResponse('Line item not found');
            return false;
        }

        static::$borrowerLineItem = new BorrowerDrawLineItem($lineItemRecord);
        return true;
    }

    /**
     * Validate file upload
     * @return bool
     */
    private static function validateFileUpload(): bool
    {
        // Check if maximum number of documents exceeded
        if (static::$borrowerLineItem->hasDocs && static::$borrowerLineItem->docCount >= self::MAX_UPLOADS_PER_LINE_ITEM) {
            static::sendErrorResponse('Maximum number of documents exceeded for this line item');
            return false;
        }

        if (!isset($_FILES['file']) || $_FILES['file']['error'] !== UPLOAD_ERR_OK) {
            static::sendErrorResponse('File upload failed or no file provided');
            return false;
        }

        $file = $_FILES['file'];

        // File type validation
        if (!in_array($file['type'], glMimeTypes::$glMimeTypes)) {
            static::sendErrorResponse('Unsupported file type: ' . $file['type']);
            return false;
        }

        // File size validation
        if ($file['size'] > CONST_GLUPLOAD_MAX_BYTESFILESIZE_ALLOWED) {
            $maxSizeMB = CONST_GLUPLOAD_MAX_BYTESFILESIZE_ALLOWED / (1024*1024);
            static::sendErrorResponse("File size too large. Maximum allowed: {$maxSizeMB}MB");
            return false;
        }

        if ($file['size'] == 0) {
            static::sendErrorResponse('Empty file not allowed');
            return false;
        }

        return true;
    }

    /**
     * Save uploaded document following the loan upload pattern
     * @return void
     */
    private static function saveUploadedDocument(): void
    {
        $infoArray = static::prepareInfoArray();
        $infoArray = static::processUploadedFile($infoArray);

        static::saveDocument($infoArray);
        static::uploadDocumentToServer($infoArray);
        static::handleDocumentAssociation();
    }

    /**
     * Prepare info array for document saving
     * @return array
     */
    private static function prepareInfoArray(): array
    {
        return [
            'userGroup' => static::$userGroup,
            'userNumber' => static::$userNumber,
            'userName' => static::$userName,
            'uploadedBy' => static::$userNumber,
            'uploadingUserType' => static::$userGroup,
            'saveTab' => 'DOC',
            'isSysNotesPrivate' => PageVariables::$isSysNotesPrivate,
            'saveNotes' => 1,
            'LMRID' => static::$LMRId,
            'eLMRID' => cypher::myEncryption(static::$LMRId),
            'PCID' => static::$loanFile->FPCID,
            'oldFPCID' => static::$loanFile->oldFPCID,
            'borrowerLName' => static::$loanFile->borrowerLName,
            'recordDate' => static::$loanFile->recordDate,
            'CLType' => 'Other',
            'docCategory' => 'Budget Docs',
            'propertyDocs' => 0,
        ];
    }

    /**
     * Process uploaded file and add file-specific data to info array
     * @param array $infoArray
     * @return array
     */
    private static function processUploadedFile(array $infoArray): array
    {
        $file = $_FILES['file'];
        $fileName = $file['name'];
        $fileTmpName = $file['tmp_name'];

        static::$fileExtension = pathinfo($fileName, PATHINFO_EXTENSION);
        static::$docName = Strings::stripQuote(Strings::removeDisAllowedChars($fileName));

        $infoArray['tmpFileContent'] = base64_encode(file_get_contents($fileTmpName));
        $infoArray['fileExtension'] = static::$fileExtension;
        $infoArray['docName'] = static::$docName;

        return $infoArray;
    }

    /**
     * Save document to tblLMRFileDocs
     * @param array $infoArray
     * @return void
     */
    private static function saveDocument(array $infoArray): void
    {
        Log::Insert('saveFileDocument::getReport - Start (Draw Line Item)');
        static::$docID = saveFileDocument::getReport($infoArray);
        Log::Insert('saveFileDocument::getReport - End (Draw Line Item)');

        if (!static::$docID) {
            static::sendErrorResponse('Document not uploaded - failed to save metadata');
        }
    }

    /**
     * Upload document to server
     * @param array $infoArray
     * @return void
     */
    private static function uploadDocumentToServer(array $infoArray): void
    {
        $docNameWithoutExt = Strings::removeDisAllowedChars(Strings::stripQuote(Strings::removeFileExtension(static::$docName)));
        $infoArray['docID'] = static::$docID;
        $infoArray['fileDocName'] = $docNameWithoutExt .
            '_' . Strings::removeDisAllowedChars(static::$loanFile->borrowerLName) .
            '_' . cypher::myEncryption(static::$docID) . '.' . static::$fileExtension;

        Log::Insert('UploadServer::upload - Start (Draw Line Item)');
        $result = UploadServer::upload($infoArray);
        Log::Insert('UploadServer::upload - End (Draw Line Item)');

        if ($result !== 'Success') {
            $docRecord = tblLMRFileDocs::Get(['docID' => static::$docID]);
            if ($docRecord) {
                $docRecord->activeStatus = 0;
                $docRecord->Save();
            }
            static::sendErrorResponse('Document not uploaded - failed to upload to server');
        }
    }

    /**
     * Associate document with line item using BorrowerDrawLineItem
     * @return void
     */
    private static function handleDocumentAssociation(): void
    {
        $currentStage = DrawStageHelper::getCurrentStage(static::$LMRId);

        $result = static::$borrowerLineItem->addDocument(
            static::$docID,
            static::$userNumber,
            static::$uploaderType,
            $currentStage
        );

        if (!$result) {
            static::sendErrorResponse('Failed to associate document with line item');
        }
    }

    /**
     * Initialize variables for delete request
     * @return void
     */
    private static function initializeDeleteVariables(): void
    {
        static::$docID = $_GET['docID'] ?? null;
        static::$userNumber = Strings::GetSess('userNumber');
        static::$userName = Strings::GetSess('firstName') . ' ' . Strings::GetSess('lastName');
        static::$userGroup = Strings::GetSess('userGroup');
    }

    /**
     * Validate delete permissions
     * @return bool
     */
    private static function validateDeletePermissions(): bool
    {
        $allowToManageDraws = PageVariables::$allowToManageDraws;
        $allowUsersDeleteUploads = SowTemplateManager::forProcessingCompany(static::$loanFile->FPCID)->getTemplate()->allowUsersDeleteUploads;
        if (!$allowToManageDraws && !$allowUsersDeleteUploads) {
            static::sendErrorResponse('You do not have permission to delete documents');
            return false;
        }
        return true;
    }

    /**
     * Perform the actual document deletion
     * @return void
     */
    private static function performDocumentDeletion(): void
    {
        $db = Database2::getInstance();
        $db->beginTransaction();
        try {
            $qry = 'UPDATE tblDrawRequestLineItemDocs
                SET activeStatus = 0
                WHERE docID = :docID';

            $result = $db->update($qry, ['docID' => static::$docID]);

            if (!$result) {
                throw new \Exception('Failed to remove document association');
            }

            $inArray = [
                'docNumber' => static::$docID,
                'userName' => static::$userName,
                'userNumber' => static::$userNumber,
                'userGroup' => static::$userGroup,
                'isSysNotesPrivate' => PageVariables::$isSysNotesPrivate,
                'LMRId' => static::$LMRId,
                'saveNotes' => 1
            ];

            $delCnt = deleteFileDocs::getReport($inArray);

            if ($delCnt <= 0) {
                throw new \Exception('Failed to delete document');
            }

            if (isset($_SESSION['ajaxUploadSession']['DocArrayAjaxUpload'][static::$docID])) {
                unset($_SESSION['ajaxUploadSession']['DocArrayAjaxUpload'][static::$docID]);
                unset($_SESSION['ajaxUploadSession']['tblLMRProcessorCommentsIds'][static::$docID]);
            }
            $db->commit();
        } catch (\Exception $e) {
            $db->rollBack();
            throw $e;
        }
    }
}
