<?php

namespace models\composite\oDrawManagement;

use models\types\strongType;
use models\lendingwise\tblDrawRequests_h;
use models\lendingwise\db\tblDrawRequests_h_db;

/**
 * Helper class to determine draw request stages for document uploads
 */
class DrawStageHelper extends strongType
{
    /**
     * Get the current stage name for document uploads
     *
     * @param int $LMRId The loan file ID
     * @return string The stage name (e.g., "Initial Scope of Work", "Draw 1", "Revision")
     */
    public static function getCurrentStage(int $LMRId): string
    {
        try {
            $drawManager = new DrawRequestManager($LMRId);
            $drawRequest = $drawManager->getDrawRequest();

            if ($drawManager->isInitialScopeOfWork()) {
                return 'Initial Scope of Work';
            }

            if ($drawRequest->isDrawRequest == 1) {
                $drawNumber = self::getDrawRequestNumber($drawRequest->id);
                return "Draw {$drawNumber}";
            }

            return 'Revision';

        } catch (\Exception $e) {
            error_log("DrawStageHelper error: " . $e->getMessage());
            return 'Unknown';
        }
    }

    /**
     * Get the draw request number (1, 2, 3, etc.)
     *
     * @param int $drawId The current draw request ID
     * @return int The draw number
     */
    private static function getDrawRequestNumber(int $drawId): int
    {
        try {
            $allDrawRequests = tblDrawRequests_h::GetAll([
                tblDrawRequests_h_db::COLUMN_DRAWID => $drawId,
                tblDrawRequests_h_db::COLUMN_ISDRAWREQUEST => 1,
                tblDrawRequests_h_db::COLUMN_STATUS => 'approved']);
            $count = count($allDrawRequests);

            return ++$count;
        } catch (\Exception $e) {
            error_log("DrawStageHelper getDrawRequestNumber error: " . $e->getMessage());
            return 1;
        }
    }
}
